using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.DependencyInjection;
using Microsoft.UI.Composition.SystemBackdrops;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Documents;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using System.Threading.Tasks;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.System;
using Windows.UI.Popups;
using FrameworkElement = Microsoft.UI.Xaml.FrameworkElement;

// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace Watcher
{
    /// <summary>
    /// An empty window that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class MainWindow : Window
    {
        public string FilePath { get; set; } = @"H:\CCTV2\594329021_1_20241018T231930Z_20241018T234931Z.mp4";
        public ObservableCollection<VehicleEntry> Vehicles { get; } = new ObservableCollection<VehicleEntry>();
        public DateTime StartTime { get; set; } // 用来记录视频的开始时间
        public TimeSpan CurrentTimeOffset = TimeSpan.Zero; // 用来记录当前播放时间偏移
        
        public DateTime CurrentTime => StartTime + CurrentTimeOffset; // 用来记录当前播放时间
        public DateTime CurrentRangeStartTime; // 用来记录当前时间范围的开始时间
        public DateTime CurrentRangeEndTime; // 用来记录当前时间范围的结束时间
        readonly PlayerViewModel player;
        readonly FileListViewModel fileList;
        readonly OverviewViewModel overviewViewModel;
        
        public MainWindow()
        {
            player = Ioc.Default.GetRequiredService<PlayerViewModel>();
            fileList = Ioc.Default.GetRequiredService<FileListViewModel>();
            overviewViewModel = Ioc.Default.GetRequiredService<OverviewViewModel>();
            
            this.InitializeComponent();
            this.ExtendsContentIntoTitleBar = false;
            SystemBackdrop = new MicaBackdrop()
            { Kind = MicaKind.BaseAlt };

            // 注册全局键盘事件处理器
            this.Content.PreviewKeyDown += KeyDownHandler;

            // 订阅文件列表选中视频改变事件
            fileList.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == "SelectedVideo")
                {
                    if(fileList.SelectedVideo != null)
                        Open(fileList.SelectedVideo.FullPath);
                }
            };
            
            // 订阅播放器播放结束事件，实现自动播放下一个视频
            player.PlaybackEnded += (sender, e) =>
            {
                Debug.WriteLine("收到播放结束事件，中断进度条拖动，尝试播放下一个视频");
                fileList.PlayNextVideo();
                // 中断进度条拖动行为，防止连续跳转
                //MyPlayer.ForceReleaseProgressSlider();
            };
            fileList.LoadVideos();

            // 同步文件列表的SelectedDate到概览ViewModel
            overviewViewModel.SelectedDate = fileList.SelectedDate;
            fileList.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(FileListViewModel.SelectedDate))
                {
                    overviewViewModel.SelectedDate = fileList.SelectedDate;
                }
            };

            // 订阅播放器时间变化事件，实时更新当前时间偏移
            player.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(PlayerViewModel.CurTimeLong))
                {
                    // 实时更新当前时间偏移，确保收费记录时间准确
                    CurrentTimeOffset = TimeSpan.FromTicks((long)player.CurTimeLong);
                }
            };
        }
        private void Open(string filePath)
        {
            
            var (startTime, _) = Tools.ExtractDates(Path.GetFileName(filePath));
            if (startTime != null)
            {
                this.player.Play(filePath);
                
                StartTime = startTime.Value;

                var (start,_) = Tools.GetTimeRange(CurrentTime);
                CurrentRangeStartTime = start;
            }

            else
                // 中文报错
                Debug.WriteLine("无法提取视频时间信息，请确保视频文件名中包含时间信息。");
                // 使用弹窗提醒错误
                //var dialog = new MessageDialog("无法提取视频时间信息，请确保视频文件名中包含时间信息。");
                //dialog.ShowAsync();
                
        }

        /// <summary>
        /// 获取当前真实时间（视频开始时间 + 播放偏移时间）
        /// </summary>
        /// <returns>当前真实时间</returns>
        private DateTime GetCurrentRealTime()
        {
            return StartTime + CurrentTimeOffset;
        }

        private async void KeyDownHandler(object sender, KeyRoutedEventArgs e)
        {
            bool handled = await HandleKeyPress(e.Key);
            if (handled)
            {
                e.Handled = true;
            }
        }
        public async Task<bool> HandleKeyPress(VirtualKey key)
        {
            switch (key)
            {
                case VirtualKey.NumberPad0:
                case VirtualKey.NumberPad1:
                case VirtualKey.NumberPad2:
                case VirtualKey.NumberPad3:
                case VirtualKey.NumberPad4:
                case VirtualKey.NumberPad5:
                case VirtualKey.NumberPad6:
                case VirtualKey.NumberPad7:
                case VirtualKey.NumberPad8:
                case VirtualKey.NumberPad9:
                    if (!player.IsPlaying)
                        return false;
                    
                    GlobalKeyboardInputService.HandleNumberKeyInput(key, "MainWindow");
                    return true;

                // 处理小键盘回车键
                case VirtualKey.Enter:
                    if (!player.IsPlaying || string.IsNullOrEmpty(overviewViewModel.CurrentInputPrice))
                        return false;
                    
                    DateTime realTime = GetCurrentRealTime();
                    
                    await GlobalKeyboardInputService.HandleEnterKeyInputAsync(key, realTime, "MainWindow");
                    return true;

                // 处理空格键 - 切换播放/暂停
                case VirtualKey.Space:
                    player.TogglePlayPause();
                    Debug.WriteLine("空格键被按下，切换播放/暂停状态");
                    return true;
                    
                case VirtualKey.Back:
                    overviewViewModel.ClearInput();
                    Debug.WriteLine("退格键被按下，清空当前输入");
                    return true;
                    
                case VirtualKey.Right or VirtualKey.Left:
                    if(player.IsPlaying)
                        HandleSeekKeyDown(key);
                    return true;

                default:
                    return false;
            }
        }
        //接受VirtualKey处理左右按键事件，实现视频的快进和后退
        private void HandleSeekKeyDown(VirtualKey key)
        {

            if (key == VirtualKey.Right)
                player.SeekForward();
            else
                player.SeekBackward();
        CurrentTimeOffset = TimeSpan.FromTicks((long)player.CurTimeLong);
        }
    }
}
